import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Pressable, Animated, Dimensions, useColorScheme } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context'; // Import SafeAreaView
import { Question, Option } from '@/types/questions'; // Using the new types
import { ProgressIndicator } from './ProgressIndicator'; // Removed ProgressIndicatorProps as it's not used here
import { getThemeColors, ThemeColors } from '@/styles/Theme'; // Import theme helpers

// Define props for the new QuestionCard
export interface QuestionCardProps {
    question: Question;
    onAnswer: (value: string | string[]) => void; // Answers will be string or array of strings
    onSkip?: () => void; // Optional skip function
    onBack?: () => void; // Optional back function
    currentIndex: number;
    totalQuestions: number;
    canGoBack?: boolean;
    selectedValues?: string[]; // Selected values will be strings
}

const { width } = Dimensions.get('window');

export const QuestionCard: React.FC<QuestionCardProps> = ({
    question,
    onAnswer,
    onSkip,
    onBack,
    currentIndex,
    totalQuestions,
    canGoBack = false,
    selectedValues = [],
}) => {
    const colorScheme = useColorScheme() || 'light';
    const colors = getThemeColors(colorScheme);
    const styles = createQuestionCardStyles(colors); // Generate styles with theme

    const [localSelected, setLocalSelected] = useState<string[]>(selectedValues);
    const slideAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        setLocalSelected(selectedValues.length > 0 ? selectedValues : []);
        slideAnim.setValue(0);
        Animated.timing(slideAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
        }).start();
    }, [currentIndex, selectedValues, slideAnim]);

    const handleOptionPress = (optionValue: string | number) => {
        const val = String(optionValue); // Ensure value is string
        if (question.allowMultiple) {
            let newSelected: string[];

            if (val === 'none') {
                // If 'none' is selected, deselect all others. If it's already selected, deselect it.
                newSelected = localSelected.includes('none') ? [] : ['none'];
            } else {
                // If another option is selected, ensure 'none' is not in the list.
                const otherSelected = localSelected.filter(v => v !== 'none');
                if (otherSelected.includes(val)) {
                    // Deselect the option
                    newSelected = otherSelected.filter(v => v !== val);
                } else {
                    // Select the option
                    newSelected = [...otherSelected, val];
                }
            }
            setLocalSelected(newSelected);
        } else {
            setLocalSelected([val]);
            // For single select, automatically proceed to next question after a short delay
            setTimeout(() => onAnswer(val), 150);
        }
    };

    const handleNext = () => {
        if (localSelected.length > 0) {
            onAnswer(question.allowMultiple ? localSelected : localSelected[0]);
        } else if (onSkip) {
            onSkip(); // Allow skipping if no answer is selected and onSkip is provided
        }
    };

    const handleBackPress = () => {
        if (onBack) {
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }).start(() => onBack());
        }
    };

    // Determine if the next button should be shown (for multiple choice or if auto-next is not desired)
    const showNextButton = question.allowMultiple || !question.options.every(op => op.emoji && !op.label);

    return (
        <SafeAreaView style={styles.safeAreaContainer}>
            <Animated.View
                style={[
                    styles.container,
                    {
                        transform: [
                            {
                                translateX: slideAnim.interpolate({
                                    inputRange: [0, 1],
                                    outputRange: [width, 0], // Slide in from the right
                                }),
                            },
                        ],
                        opacity: slideAnim,
                    },
                ]}
            >
                <ProgressIndicator current={currentIndex} total={totalQuestions} />

                <View style={styles.questionContent}>
                    <Text style={styles.questionText}>{question.text}</Text>

                    <View style={styles.optionsContainer}>
                        {question.options.map((option) => (
                            <Pressable
                                key={String(option.value)}
                                style={[
                                    styles.optionButton,
                                    localSelected.includes(String(option.value)) && styles.selectedOption,
                                    // Make emoji-only options larger if needed
                                    (!option.label && option.emoji) ? styles.emojiOnlyOption : {}
                                ]}
                                onPress={() => handleOptionPress(option.value)}
                            >
                                {option.emoji && <Text style={[styles.emoji, (!option.label && option.emoji) ? styles.emojiOnlyText : {}]}>{option.emoji}</Text>}
                                {option.label && <Text style={[styles.optionLabel, localSelected.includes(String(option.value)) && styles.selectedLabel]}>{option.label}</Text>}
                            </Pressable>
                        ))}
                    </View>
                </View>

                <View style={styles.navigationButtons}>
                    {canGoBack && onBack && (
                        <Pressable style={styles.navButton} onPress={handleBackPress}>
                            <Text style={styles.navButtonText}>Back</Text>
                        </Pressable>
                    )}
                    {showNextButton && (
                        <Pressable
                            style={[styles.navButton, styles.nextButton, (localSelected.length === 0 && question.allowMultiple) && styles.disabledButton]}
                            onPress={handleNext}
                            disabled={localSelected.length === 0 && question.allowMultiple}
                        >
                            <Text style={[styles.navButtonText, styles.nextButtonText]}>Next</Text>
                        </Pressable>
                    )}
                </View>
            </Animated.View>
        </SafeAreaView>
    );
};

// Moved StyleSheet.create into a function that accepts theme colors
const createQuestionCardStyles = (colors: ThemeColors) => StyleSheet.create({
    safeAreaContainer: { // Style for SafeAreaView
        flex: 1,
        backgroundColor: colors.background, // Match main container background
    },
    container: {
        flex: 1,
        backgroundColor: colors.background, // Use theme background
    },
    questionContent: {
        flex: 1,
        paddingHorizontal: 24,
        paddingVertical: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    questionText: {
        fontSize: 22,
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: 30,
        color: colors.text, // Use theme text color
    },
    optionsContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: 12,
        width: '100%',
    },
    optionButton: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 12,
        borderRadius: 10,
        backgroundColor: colors.selectionBackground, // Use theme selection background
        borderWidth: 2,
        borderColor: 'transparent',
        minWidth: 90,
        minHeight: 70,
    },
    selectedOption: {
        backgroundColor: colors.selectionActiveBackground, // Use theme active selection background
        borderColor: colors.accent, // Use theme accent for border
    },
    emojiOnlyOption: {
        minWidth: 70,
        minHeight: 70,
        padding: 10,
    },
    emoji: {
        fontSize: 28,
        marginBottom: 4,
        color: colors.text, // Use theme text for emoji color for consistency
    },
    emojiOnlyText: {
        fontSize: 36,
        marginBottom: 0,
    },
    optionLabel: {
        fontSize: 14,
        color: colors.selectionText, // Use theme selection text color
        textAlign: 'center',
    },
    selectedLabel: {
        color: colors.selectionActiveText, // Use theme active selection text color
        fontWeight: '500',
    },
    navigationButtons: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingHorizontal: 24,
        paddingVertical: 20,
        borderTopWidth: 1,
        borderTopColor: colors.divider, // Use theme divider color
        backgroundColor: colors.background, // Use theme background
    },
    navButton: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 8,
        alignItems: 'center',
        minWidth: 100,
    },
    navButtonText: {
        fontSize: 16,
        fontWeight: '500',
        color: colors.accent, // Use theme accent for back button text
    },
    nextButton: {
        backgroundColor: colors.accent, // Use theme accent for next button background
    },
    nextButtonText: {
        color: colors.accentText, // Use theme accent text for next button text
    },
    disabledButton: {
        backgroundColor: colors.border, // Use a less prominent color like border for disabled state
        // Consider adding a disabled text color from theme if available, or adjust opacity
    },
}); 