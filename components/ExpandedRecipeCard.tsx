import React, { useState } from 'react';
import { View, Image, TouchableOpacity, useColorScheme, Platform } from 'react-native';
import { Card, Text, IconButton, Button, Divider, Snackbar, ActivityIndicator } from 'react-native-paper';
import ShoppingCart from '@/assets/images/icons/shopping-cart.svg';
import createRecipeStyles from '@/styles/RecipeStyles';
import { getThemeColors } from '@/styles/Theme';
import { RecipeVariant, InstructionType, Ingredient, RecipeInstructions } from './types';
import { useGroceryList } from '@/contexts/GroceryListContext';
import { useInventory } from '@/contexts/InventoryContext';
import { computeIngredientAvailability } from '@/utils/ingredientUtils';

interface ExpandedRecipeCardProps {
  title: string;
  timeInMinutes: number;
  calories: number;
  imageUrl: string;
  instructions: RecipeInstructions;
  ingredients: Ingredient[];
  isFavorite: boolean;
  servings: number;
  selectedVariant: RecipeVariant;
  instructionType: InstructionType;
  recipeVariants: RecipeVariant[];
  instructionTypes: InstructionType[];
  isLoading?: boolean;
  onToggleFavorite: (e: any) => void;
  onSelectVariant: (variant: RecipeVariant) => void;
  onSelectInstructionType: (type: InstructionType) => void;
  onChangeServings: (servings: number) => void;
  onPress: () => void;
}

const ExpandedRecipeCard: React.FC<ExpandedRecipeCardProps> = ({
  title,
  timeInMinutes,
  calories,
  imageUrl,
  instructions,
  ingredients,
  isFavorite,
  servings,
  selectedVariant,
  instructionType,
  recipeVariants,
  instructionTypes,
  isLoading = false,
  onToggleFavorite,
  onSelectVariant,
  onSelectInstructionType,
  onChangeServings,
  onPress,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const RecipeStyles = createRecipeStyles(colorScheme as 'light' | 'dark');
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const { addItem, addItems } = useGroceryList();
  const { isIngredientAvailable } = useInventory();

  // Compute real-time ingredient availability
  const ingredientsWithAvailability = computeIngredientAvailability(ingredients, isIngredientAvailable);

  const addIngredientToGroceryList = async (ingredientName: string) => {
    try {
      await addItem(ingredientName);
      showMessage(`Added ${ingredientName} to grocery list`);
    } catch (error) {
      console.error('Error adding ingredient to grocery list:', error);
      showMessage('Failed to add ingredient to grocery list');
    }
  };

  const addAllIngredientsToGroceryList = async () => {
    try {
      // Filter out ingredients that are already available
      const unavailableIngredients = ingredientsWithAvailability
        .filter((ingredient) => !ingredient.available)
        .map((ingredient) => ingredient.name);

      if (unavailableIngredients.length === 0) {
        showMessage('All ingredients are already available');
        return;
      }

      await addItems(unavailableIngredients);
      showMessage(`Added ${unavailableIngredients.length} unavailable ingredients to grocery list`);
    } catch (error) {
      console.error('Error adding all ingredients to grocery list:', error);
      showMessage('Failed to add ingredients to grocery list');
    }
  };

  const showMessage = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  return (
    <Card style={RecipeStyles.recipeCard} onPress={onPress}>
      <View style={RecipeStyles.recipeCardContent}>
        <View style={RecipeStyles.recipeHeader}>
          <View style={RecipeStyles.recipeHeaderContent}>
            <Text style={RecipeStyles.recipeTitle}>{title}</Text>
            <Text style={RecipeStyles.recipeInfo}>
              {timeInMinutes} min · {calories} calories per serving
            </Text>
          </View>
          <IconButton
            icon={isFavorite ? 'heart' : 'heart-outline'}
            iconColor={colors.accent}
            size={24}
            onPress={(e) => {
              e.stopPropagation();
              onToggleFavorite(e);
            }}
            style={RecipeStyles.favoriteButton}
          />
        </View>

        <View style={RecipeStyles.variantsContainer}>
          {recipeVariants.map((variant) => (
            <TouchableOpacity
              key={variant}
              style={[RecipeStyles.variantChip, selectedVariant === variant && { backgroundColor: colors.accent }]}
              onPress={() => onSelectVariant(variant)}
            >
              <Text
                style={[RecipeStyles.variantText, selectedVariant === variant && { color: colors.selectionActiveText }]}
              >
                {variant}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Recipe Image */}
        <View style={RecipeStyles.expandedImageContainer}>
          <Image source={{ uri: imageUrl }} style={RecipeStyles.recipeImage} resizeMode='cover' />
        </View>

        {/* Loading Indicator */}
        {isLoading && (
          <View style={{ alignItems: 'center', padding: 20 }}>
            <ActivityIndicator size='large' color={colors.accent} />
            <Text style={{ marginTop: 10, color: colors.text, textAlign: 'center' }}>Loading recipe details...</Text>
          </View>
        )}

        {/* Ingredients and Instructions Sections - only show when not loading */}
        {!isLoading && (
          <View>
            {/* Ingredients Section */}
            <View style={RecipeStyles.sectionContainer}>
              <Text style={RecipeStyles.sectionTitle}>Ingredients</Text>

              <View style={RecipeStyles.servingsContainer}>
                <Text style={RecipeStyles.servingsLabel}>Servings</Text>
                <TouchableOpacity
                  style={RecipeStyles.servingButton}
                  onPress={() => onChangeServings(Math.max(1, servings - 1))}
                >
                  <Text style={RecipeStyles.servingButtonText}>−</Text>
                </TouchableOpacity>
                <Text style={RecipeStyles.servingsCount}>{servings}</Text>
                <TouchableOpacity style={RecipeStyles.servingButton} onPress={() => onChangeServings(servings + 1)}>
                  <Text style={RecipeStyles.servingButtonText}>+</Text>
                </TouchableOpacity>
              </View>

              {/* Ingredients List */}
              <View>
                {ingredientsWithAvailability.map((ingredient, index) => (
                  <View
                    key={index}
                    style={!ingredient.available ? RecipeStyles.unavailableIngredientRow : RecipeStyles.ingredientRow}
                  >
                    <Text
                      style={[
                        RecipeStyles.ingredientText,
                        !ingredient.available && RecipeStyles.unavailableIngredientText,
                      ]}
                    >
                      {ingredient.name}
                    </Text>
                    {!ingredient.available && (
                      <IconButton
                        icon={() => <ShoppingCart fill={colors.accent} width={18} height={18} />}
                        onPress={() => addIngredientToGroceryList(ingredient.name)}
                      />
                    )}
                  </View>
                ))}
              </View>

              <Button
                mode='outlined'
                style={RecipeStyles.addAllButton}
                icon={() => <ShoppingCart fill={colors.accent} width={18} height={18} />}
                onPress={addAllIngredientsToGroceryList}
                textColor={colors.accent}
              >
                Add All to Grocery List
              </Button>
            </View>

            <Divider />

            {/* Instructions Section */}
            <View style={RecipeStyles.sectionContainer}>
              <Text style={RecipeStyles.sectionTitle}>Instructions</Text>

              {/* Instruction Types */}
              <View style={RecipeStyles.instructionTypesContainer}>
                {instructionTypes.map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      RecipeStyles.instructionTypeChip,
                      instructionType === type && { backgroundColor: colors.accent },
                    ]}
                    onPress={() => onSelectInstructionType(type)}
                  >
                    <Text
                      style={[
                        RecipeStyles.instructionTypeText,
                        instructionType === type && { color: colors.selectionActiveText },
                      ]}
                    >
                      {type}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <Text style={RecipeStyles.instructionsText}>
                {instructionType === InstructionType.HIGH_LEVEL && instructions[InstructionType.HIGH_LEVEL]}
                {instructionType === InstructionType.DETAILED && instructions[InstructionType.DETAILED]}
                {instructionType === InstructionType.TEACH_MODE && instructions[InstructionType.TEACH_MODE]}
              </Text>
            </View>
          </View>
        )}

        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          style={{
            bottom: Platform.OS === 'ios' ? 90 : 70, // Position above the tab bar
          }}
          action={{
            label: 'OK',
            onPress: () => setSnackbarVisible(false),
          }}
        >
          {snackbarMessage}
        </Snackbar>
      </View>
    </Card>
  );
};

export default ExpandedRecipeCard;
