import React from 'react';
import { View, ScrollView, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';
import { RecipeSource } from '@/components/types';
import { RECIPE_SOURCES } from '@/constants/RecipeConstants';

interface RecipeSourceTabsProps {
  selectedSource: RecipeSource;
  onSourceChange: (source: RecipeSource) => void;
  onTabPress?: () => void;
}

const RecipeSourceTabs: React.FC<RecipeSourceTabsProps> = ({ selectedSource, onSourceChange, onTabPress }) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{
        alignItems: 'center',
      }}
      style={{
        backgroundColor: colors.background,
      }}
    >
      {RECIPE_SOURCES.map((source) => (
        <TouchableOpacity
          key={source}
          style={{
            alignItems: 'center',
            paddingVertical: 8,
            paddingHorizontal: 8,
          }}
          onPress={() => {
            onSourceChange(source);
            onTabPress?.();
          }}
        >
          <Text
            style={{
              fontSize: 16,
              fontWeight: selectedSource === source ? 'bold' : '500',
              color: selectedSource === source ? colors.accent : colors.text,
              marginBottom: 4,
              textAlign: 'center',
            }}
            numberOfLines={1}
          >
            {source}
          </Text>
          {selectedSource === source && (
            <View
              style={{
                height: 3,
                width: '100%',
                backgroundColor: colors.accent,
                borderRadius: 1.5,
              }}
            />
          )}
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

export default RecipeSourceTabs;
