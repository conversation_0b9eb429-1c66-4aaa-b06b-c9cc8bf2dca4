import React from 'react';
import { View, Text, TouchableOpacity, useColorScheme } from 'react-native';
import { IconButton } from 'react-native-paper';
import { getThemeColors } from '@/styles/Theme';
import createInventoryStyles from '@/styles/InventoryStyles';
import { InventoryItem as InventoryItemType } from '@/components/types';

interface InventoryItemProps {
  item: InventoryItemType;
  onIncrement: (itemName: string) => void;
  onDecrement: (itemName: string) => void;
  onRemove: (itemName: string) => void;
}

const InventoryItemComponent: React.FC<InventoryItemProps> = ({
  item,
  onIncrement,
  onDecrement,
  onRemove,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const styles = createInventoryStyles(colors);

  const handleDecrement = () => {
    if (item.quantity <= 1) {
      onRemove(item.name);
    } else {
      onDecrement(item.name);
    }
  };

  return (
    <View style={styles.itemRow}>
      <Text 
        style={[
          styles.itemName,
          item.quantity > 0 ? styles.itemNameActive : styles.itemNameInactive
        ]}
      >
        {item.name}
      </Text>
      <View style={styles.quantityControls}>
        <IconButton
          icon="trash-can-outline"
          size={20}
          iconColor={colors.textSecondary}
          onPress={() => onRemove(item.name)}
          style={styles.iconButton}
        />
        <IconButton
          icon="minus"
          size={20}
          iconColor={colors.textSecondary}
          onPress={handleDecrement}
          style={styles.iconButton}
        />
        <Text style={styles.quantityText}>{item.quantity}</Text>
        <IconButton
          icon="plus"
          size={20}
          iconColor={colors.accent}
          onPress={() => onIncrement(item.name)}
          style={styles.iconButton}
        />
      </View>
    </View>
  );
};

export default InventoryItemComponent;
