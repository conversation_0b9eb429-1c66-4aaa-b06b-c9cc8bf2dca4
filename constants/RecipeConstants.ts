import { MealType, MealTypeItem, RecipeSource } from '@/components/types';

export const MAX_RECIPES_PER_MEAL_TYPE = 4;

export const MEAL_TYPE_FILTERS: MealTypeItem[] = [
  { title: 'All', icon: 'food-fork-drink' },
  { title: MealType.BREAKFAST, icon: 'egg-fried' },
  { title: MealType.LUNCH, icon: 'rice' },
  { title: MealType.DINNER, icon: 'pot-steam' },
  { title: MealType.DESSERT, icon: 'cupcake' },
];

export const RECIPE_SOURCES: RecipeSource[] = [RecipeSource.FOR_YOU, RecipeSource.FROM_INVENTORY, RecipeSource.FAVORITES];
