import { Recipe, MealType, InstructionType } from '@/components/types';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { UnsplashService } from '@/services/UnsplashService';

/**
 * Service for fetching recipes from various sources
 */
export class RecipeService {
  /**
   * Fetch recipes from Firestore for a specific user
   *
   * @param userId The user's UID
   * @returns Array of recipes from Firestore, or empty array if none found
   */
  static async fetchRecipesFromFirestore(userId: string): Promise<Recipe[]> {
    try {
      console.log('Fetching recipes from Firestore for user:', userId);

      // Fetch the recipes document for this user
      const recipesDoc = await firestoreRepository.getDocument(FirestoreCollections.GENERATED_RECIPES, userId);

      if (!recipesDoc || !recipesDoc.recipes || !Array.isArray(recipesDoc.recipes)) {
        console.log('No recipes found in Firestore for user:', userId);
        return [];
      }

      const firestoreRecipes = recipesDoc.recipes;
      console.log(`Found ${firestoreRecipes.length} recipes in Firestore`);

      // Transform Firestore recipes to match our Recipe interface
      const transformedRecipes: Recipe[] = await Promise.all(
        firestoreRecipes.map(async (recipe: any) => {
          // Generate image URL if not present or if imageQuery is available
          let imageUrl = recipe.imageUrl;
          if (!imageUrl && recipe.imageQuery) {
            try {
              const imageResponse = await UnsplashService.getImageUrl(recipe.imageQuery);
              if (imageResponse.success && imageResponse.imageUrl) {
                imageUrl = imageResponse.imageUrl;
              } else {
                console.warn('Failed to fetch image for recipe:', recipe.title, imageResponse.error);
                imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
              }
            } catch (error) {
              console.warn('Failed to fetch image for recipe:', recipe.title, error);
              imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
            }
          } else if (!imageUrl) {
            imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
          }

          // Transform instructions from Python format to TypeScript format
          let instructions = {
            [InstructionType.HIGH_LEVEL]: 'Instructions not available',
            [InstructionType.DETAILED]: 'Instructions not available',
            [InstructionType.TEACH_MODE]: 'Instructions not available',
          };

          if (recipe.instructions) {
            instructions = {
              [InstructionType.HIGH_LEVEL]:
                recipe.instructions[InstructionType.HIGH_LEVEL] || 'Instructions not available',
              [InstructionType.DETAILED]: recipe.instructions[InstructionType.DETAILED] || 'Instructions not available',
              [InstructionType.TEACH_MODE]:
                recipe.instructions[InstructionType.TEACH_MODE] || 'Instructions not available',
            };
          }

          // Ensure the recipe has all required fields with defaults
          return {
            id: recipe.id || `firestore-${Math.random().toString(36).substring(2, 9)}`,
            title: recipe.title || 'Untitled Recipe',
            timeInMinutes: recipe.timeInMinutes || 30,
            calories: recipe.calories || 400,
            imageUrl,
            compatibleDiets: recipe.compatibleDiets || [],
            ingredients: recipe.ingredients || [],
            instructions,
            mealType: recipe.mealType || MealType.LUNCH,
          };
        })
      );

      return transformedRecipes;
    } catch (error) {
      console.error('Error fetching recipes from Firestore:', error);
      return [];
    }
  }
}
