import { useState } from 'react';
import { RecipeVariant, InstructionType } from '@/components/types';

export interface UseRecipePreferencesReturn {
  servings: number;
  selectedVariant: RecipeVariant;
  instructionType: InstructionType;
  setServings: (servings: number) => void;
  setSelectedVariant: (variant: RecipeVariant) => void;
  setInstructionType: (type: InstructionType) => void;
}

export const useRecipePreferences = (): UseRecipePreferencesReturn => {
  const [servings, setServings] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState<RecipeVariant>('Basic');
  const [instructionType, setInstructionType] = useState<InstructionType>(InstructionType.HIGH_LEVEL);

  return {
    servings,
    selectedVariant,
    instructionType,
    setServings,
    setSelectedVariant,
    setInstructionType,
  };
};
