import React, { useState } from 'react';
import { View, Text, useColorScheme, TouchableOpacity } from 'react-native';
import { getThemeColors } from '@/styles/Theme';
import createInventoryStyles from '@/styles/InventoryStyles';
import { InventoryCategory as InventoryCategoryType } from '@/components/types';
import InventoryItemComponent from './InventoryItem';
import AddInventoryItem from './AddInventoryItem';

interface InventoryCategoryProps {
  category: InventoryCategoryType;
  onIncrement: (itemName: string) => void;
  onDecrement: (itemName: string) => void;
  onRemove: (itemName: string) => void;
  onAddItem: (itemName: string, categoryName: string) => void;
}

const InventoryCategoryComponent: React.FC<InventoryCategoryProps> = ({
  category,
  onIncrement,
  onDecrement,
  onRemove,
  onAddItem,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const styles = createInventoryStyles(colors);
  const [showAddItem, setShowAddItem] = useState(false);

  const handleAddItem = (itemName: string, categoryName: string) => {
    onAddItem(itemName, categoryName);
    setShowAddItem(false);
  };

  return (
    <View style={styles.categoryContainer}>
      <View style={styles.categoryHeader}>
        <Text style={styles.categoryEmoji}>{category.emoji}</Text>
        <Text style={styles.categoryTitle}>{category.name}</Text>
      </View>

      {category.items.map((item, index) => (
        <InventoryItemComponent
          key={`${item.name}-${index}`}
          item={item}
          onIncrement={onIncrement}
          onDecrement={onDecrement}
          onRemove={onRemove}
        />
      ))}

      {showAddItem ? (
        <AddInventoryItem
          categoryName={category.name}
          onAddItem={handleAddItem}
          onCancel={() => setShowAddItem(false)}
        />
      ) : (
        <TouchableOpacity
          style={styles.addItemButton}
          onPress={() => setShowAddItem(true)}
        >
          <Text style={styles.addItemButtonText}>Add Item</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default InventoryCategoryComponent;
