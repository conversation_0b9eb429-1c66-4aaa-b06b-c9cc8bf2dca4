import { Ingredient } from '@/components/types';

/**
 * Utility function to compute ingredient availability based on inventory check function
 * @param ingredients Array of ingredients
 * @param isIngredientAvailable Function to check if an ingredient is available in inventory
 * @returns Array of ingredients with computed availability
 */
export const computeIngredientAvailability = (
  ingredients: Ingredient[],
  isIngredientAvailable: (name: string) => boolean
): Ingredient[] => {
  return ingredients.map(ingredient => ({
    ...ingredient,
    available: isIngredientAvailable(ingredient.name)
  }));
};

/**
 * Create ingredients array with computed availability
 * @param ingredientNames Array of ingredient names
 * @param isIngredientAvailable Function to check if an ingredient is available in inventory
 * @returns Array of ingredients with computed availability
 */
export const createIngredientsWithAvailability = (
  ingredientNames: string[],
  isIngredientAvailable: (name: string) => boolean
): Ingredient[] => {
  return ingredientNames.map(name => ({
    name,
    available: isIngredientAvailable(name)
  }));
};
