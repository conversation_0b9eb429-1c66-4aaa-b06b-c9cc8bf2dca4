import React, { useState } from 'react';
import { View } from 'react-native';
import { Menu, Button } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';
import { FilterMealType } from '@/components/types';
import { MEAL_TYPE_FILTERS } from '@/constants/RecipeConstants';

interface MealTypeFilterProps {
  selectedMealType: FilterMealType;
  onMealTypeChange: (mealType: FilterMealType) => void;
}

const MealTypeFilter: React.FC<MealTypeFilterProps> = ({
  selectedMealType,
  onMealTypeChange,
}) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');

  const handleMealTypeSelect = (mealType: FilterMealType) => {
    onMealTypeChange(mealType);
    setMenuVisible(false);
  };

  return (
    <View style={{ paddingHorizontal: 16, paddingVertical: 8 }}>
      <Menu
        visible={menuVisible}
        onDismiss={() => setMenuVisible(false)}
        anchor={
          <Button
            mode='outlined'
            onPress={() => setMenuVisible(true)}
            icon='chevron-down'
            contentStyle={{ flexDirection: 'row-reverse' }}
            style={{ borderColor: colors.accent }}
            textColor={colors.text}
          >
            Filter by: {selectedMealType}
          </Button>
        }
      >
        {MEAL_TYPE_FILTERS.map((meal) => (
          <Menu.Item
            key={meal.title}
            onPress={() => handleMealTypeSelect(meal.title)}
            title={meal.title}
            leadingIcon={meal.icon}
          />
        ))}
      </Menu>
    </View>
  );
};

export default MealTypeFilter;
