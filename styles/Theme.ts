import { Colors } from '@/constants/Colors';

/**
 * Centralized theme definition for the app
 * This file contains all color definitions used across the app
 */

export const AppTheme = {
  light: {
    // Base colors
    background: Colors.light.background,
    surface: '#fff',                      // Card and surface backgrounds
    text: Colors.light.text,              // Primary text
    textSecondary: '#666',                // Secondary text, captions, etc.
    tint: Colors.light.tint,              // System tint color
    icon: Colors.light.icon,              // Icon color

    // UI Elements
    border: '#ddd',                       // Borders, dividers
    divider: '#E5E5E5',                   // Divider lines
    shadow: '#000',                       // Shadow color

    // Interactive elements
    accent: '#FF7052',                    // Primary accent color for the app
    accentText: '#FFFFFF',                // Text on accent color
    buttonBackground: '#F8F8F8',          // Default button background

    // Input elements
    inputBackground: '#F5F5F5',           // Text input background
    inputText: '#11181C',                 // Text input text color
    inputPlaceholder: '#888',             // Placeholder text color

    // Message bubbles
    userBubble: '#FF7052',                // User message bubble
    userBubbleText: '#FFFFFF',            // User message text
    botBubble: '#F0F0F0',                 // Bot message bubble
    botBubbleText: '#11181C',             // Bot message text

    // Selection components
    selectionBackground: '#FFF1EE',       // Unselected option background
    selectionText: '#11181C',             // Unselected option text
    selectionActiveBackground: '#FF7052', // Selected option background
    selectionActiveText: '#FFFFFF',       // Selected option text

    // Status indicators
    statusText: '#555',                   // Status text (typing indicator, timestamps)
  },
  dark: {
    // Base colors
    background: Colors.dark.background,
    surface: '#1E1E1E',                   // Card and surface backgrounds
    text: Colors.dark.text,               // Primary text
    textSecondary: '#9BA1A6',             // Secondary text, captions, etc.
    tint: Colors.dark.tint,               // System tint color
    icon: Colors.dark.icon,               // Icon color

    // UI Elements
    border: '#444',                       // Borders, dividers
    divider: '#333333',                   // Divider lines
    shadow: '#000',                       // Shadow color

    // Interactive elements
    accent: '#FF7052',                    // Primary accent color for the app
    accentText: '#FFFFFF',                // Text on accent color
    buttonBackground: '#2C2C2C',          // Default button background

    // Input elements
    inputBackground: '#2C2C2C',           // Text input background
    inputText: '#ECEDEE',                 // Text input text color
    inputPlaceholder: '#9BA1A6',          // Placeholder text color

    // Message bubbles
    userBubble: '#FF7052',                // User message bubble
    userBubbleText: '#FFFFFF',            // User message text
    botBubble: '#2C2C2C',                 // Bot message bubble
    botBubbleText: '#ECEDEE',             // Bot message text

    // Selection components
    selectionBackground: '#3A2A26',       // Unselected option background
    selectionText: '#ECEDEE',             // Unselected option text
    selectionActiveBackground: '#FF7052', // Selected option background
    selectionActiveText: '#FFFFFF',       // Selected option text

    // Status indicators
    statusText: '#9BA1A6',                // Status text (typing indicator, timestamps)
  }
};

// Helper function to get theme colors based on the current color scheme
export const getThemeColors = (colorScheme: 'light' | 'dark') => {
  return AppTheme[colorScheme];
};

export type ThemeColors = typeof AppTheme.light;

export default {
  AppTheme,
  getThemeColors
};
