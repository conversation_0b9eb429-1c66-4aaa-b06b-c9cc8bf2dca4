import { StyleSheet } from 'react-native';
import { ThemeColors } from './Theme'; // Assuming ThemeColors is exported from Theme.ts

export const createOnboardingStyles = (colors: ThemeColors) => {
    return StyleSheet.create({
        container: {
            flex: 1,
            backgroundColor: colors.background, // Use theme background
        },
        containerCentered: {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
            backgroundColor: colors.background, // Use theme background
        },
        completionText: {
            fontSize: 18,
            fontWeight: 'bold',
            textAlign: 'center',
            color: colors.text, // Use theme text color
        }
    });
}; 