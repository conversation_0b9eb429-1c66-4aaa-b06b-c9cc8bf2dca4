import { StyleSheet } from 'react-native';
import { ThemeColors } from './Theme';

/**
 * Create styles for the Inventory screen based on the current theme
 */
const createInventoryStyles = (colors: ThemeColors) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      marginBottom: 80, // Add extra padding for the tab bar
    },
    header: {
      backgroundColor: colors.background,
      elevation: 0,
      shadowOpacity: 0,
    },
    title: {
      alignItems: 'center',
    },
    content: {
      flex: 1,
      paddingHorizontal: 16,
    },
    categoryContainer: {
      marginBottom: 24,
    },
    categoryHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    categoryTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      marginLeft: 8,
      color: colors.text,
    },
    categoryEmoji: {
      fontSize: 24,
    },
    itemRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.divider,
    },
    itemName: {
      fontSize: 16,
      color: colors.text,
      flex: 1,
    },
    itemNameActive: {
      color: colors.accent,
    },
    itemNameInactive: {
      color: colors.textSecondary,
    },
    quantityControls: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    quantityText: {
      fontSize: 16,
      fontWeight: '500',
      marginHorizontal: 12,
      minWidth: 20,
      textAlign: 'center',
      color: colors.text,
    },
    iconButton: {
      padding: 8,
    },
    emptyStateContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
      marginVertical: 24,
    },
    emptyStateText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: 16,
    },
    emptyStateTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    addButton: {
      position: 'absolute',
      bottom: 16,
      right: 16,
      backgroundColor: colors.accent,
      borderRadius: 28,
      width: 56,
      height: 56,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 4,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
    },
    addItemButton: {
      borderWidth: 1,
      borderColor: colors.divider,
      borderStyle: 'dashed',
      borderRadius: 8,
      padding: 12,
      marginTop: 8,
      alignItems: 'center',
      justifyContent: 'center',
    },
    addItemButtonText: {
      color: colors.accent,
      fontSize: 16,
      fontWeight: '500',
    },
  });
};

export default createInventoryStyles;
