import { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { FirestoreCollections, firestoreRepository } from '@/repositories/firestoreRepository';
import { useAuth } from '@/contexts/AuthContext';

export default function IndexRedirect() {
  const router = useRouter();
  const { user, loading } = useAuth();

  useEffect(() => {
    const checkAndRedirect = async () => {
      // Wait for auth to finish loading
      if (loading) return;

      try {
        if (user) {
          const doc = await firestoreRepository.getDocument(FirestoreCollections.DIET_PREFERENCES, user.uid);
          if (doc) {
            router.replace('/(tabs)');
          } else {
            router.replace('/onboarding');
          }
        }
      } catch (err) {
        console.error('Redirect error:', err);
      }
    };

    checkAndRedirect();
  }, [user, loading, router]);

  return null;
}
